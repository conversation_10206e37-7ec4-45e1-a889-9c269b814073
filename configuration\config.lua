-----------------支持、脚本和更多信息----------------
--------------- https://discord.gg/wasabiscripts  -------------
---------------------------------------------------------------
Config                   = {}
------------------------------------------------------------------------------
-- 通过在“customize”目录中的依赖项 "wasabi_bridge"，可以实现文本UI、通知、框架和其他集成功能 --
-- 以及在 "customize" 目录中通过依赖项 "wasabi_bridge" 来创建 --
------------------------------------------------------------------------------

Config.CheckForUpdates   = true -- 推荐使用

-- 语言选项有
-- 'en' (英语)
-- 'fr' (法语)
-- 'cn' (简体中文)
-- 'tw' (繁体中文)
-- 'de' (德语)
-- 'it' (意大利语)
-- 'jp' (日语)
-- 'ko' (韩语)
-- 'pl' (波兰语)
-- 'pt' (葡萄牙语)
-- 'es' (西班牙语)
-- 'hi' (印地语)
-- 'nl' (荷兰语)
-- 'da' (丹麦语)
-- 'cs' (捷克语)
-- 如果您希望我们添加一种语言，请加入我们的discord并创建一个工单！
-- 所有区域字符串都可以在 configuration/locales/ 中找到
Config.Language          = 'en'

Config.UIColor           = false -- UI元素的色彩或设置为false以使用默认的bridge色彩

Config.toggleLockKey     =
'U'                                                                                            -- 注册为车辆锁定切换的键是哪个？
Config.toggleSound       = { audioName = 'BUTTON', audioRef = 'MP_PROPERTIES_ELEVATOR_DOORS' } -- 锁定/解锁车辆时播放的声音。留空则不播放声音。参考：gtaforums.com/topic/795622-audio-for-mods
Config.lockDistance      = 10.0                                                                -- 在哪个距离内可以解锁最近的车辆？

Config.progressBar       = true                                                               -- 如果为true，将使用进度条而不是进度圆圈。

Config.lockNPCVehicles   = true                                                               -- 锁定所有NPC车辆？

Config.robPedKeys        = {                                                                   -- 能够在枪口下抢夺行人的车钥匙
    enabled = true,                                                                            -- 启用在枪口下抢夺行人车钥匙的功能？
    timeToRob = 4,                                                                             -- 抢夺行人所需的时间（秒数）
    label = '正在偷窃钥匙...'
}

Config.notifyPolice = {
    enabled = true,      -- 当撬锁、热启动和/或抢劫行人时通知警察的概率
    hotwire = 100,        -- 热启动时通知警察的概率百分比（50 = 50%的概率）
    lockpick = 100,       -- 撬锁时通知警察的概率百分比（30 = 30%的概率）
    robPed = 100,         -- 抢劫行人时通知警察的概率百分比（70 = 70%的概率）
    blip = {
        enabled = true,  -- 当被通知时在地图上启用警察的标记？
        sprite = 161,    -- 标记精灵
        color = 1,       -- 标记颜色
        scale = 0.7,     -- 标记比例
        short = false,   -- 短标记
        flashing = true, -- 标记闪烁
        duration = 20    -- 地图上标记的持续时间（秒）
    },
    policeJobs = {       -- 被视为需要通知警察的工作岗位
        'police',
        'sheriff',
        --      'highway'
    }
}

Config.DisableAutoStart = false -- 使用钥匙进入时禁用车辆自动启动？(需要启用 Config.EngineToggle 或脚本)

Config.EngineToggle = {
    enabled = true,                -- 启用引擎切换功能？
    hotkey = 47,                   -- 默认 'G'(47) / https://docs.fivem.net/docs/game-references/controls/
    string = '[G] - 启动引擎',     -- 想要在文本用户界面显示的字符串，或为 false 不显示文本提示
}

Config.metadataKeys = { -- 元数据键选项（需要将库存集成到 wasabi_bridge）
    enabled = true,         -- 启用元数据键功能？
    keyItem = 'carkeys',     -- 用作车钥匙的物品
}

Config.givingKeys = { -- 给予钥匙选项（仅当禁用元数据键时）
    enabled = true,          -- 启用给予钥匙功能？
    command = 'givekey',     -- 给予钥匙的命令
    removeKey = true,        -- 从给予钥匙的人那里移除钥匙
    menuPosition =
    'bottom-right'           -- 选择玩家选择菜单的位置。选项：'top-left' 或 'top-right' 或 'bottom-left' 或 'bottom-right'
}

Config.manageKeys = { -- 通过菜单管理手上的钥匙（仅当禁用元数据键时）
    enabled = true,          -- 启用此功能
    command = 'managekeys',  -- 打开钥匙管理菜单的命令（如果不希望显示，则设置为 false）
    allowRemove = true,      -- 允许移除给予的钥匙
    menuPosition =
    'bottom-right'           -- 选择钥匙选择菜单的位置。选项：'top-left' 或 'top-right' 或 'bottom-left' 或 'bottom-right'
}

Config.noKeysNeeded = { -- 不需要钥匙的车辆模型
    'BMX',
    'BMXST',
    'CRUISER',
    'FIXTER',
    'SCORCHER',
    'TRIBIKE',
    'TRIBIKE2',
    'TRIBIKE3',
    -- 如果需要，在此处添加更多
}


Config.WhitelistedJobs   = { -- 无需钥匙即可进入的工作岗位
    'police',
    'sheriff',
    -- 按需添加更多
}

Config.WhitelistedPlates = { -- 无需钥匙即可驾驶的车辆牌照
    'ADMIN',
    -- 按需添加更多牌照
}

Config.hotwire           = {  -- 热启动选项
    enabled = true,           -- 启用热启动功能？
    string = '[H] - 热启动', -- 文本用户界面中希望显示的字符串
    hotkey = 74,              -- 默认 'H'(74) / https://docs.fivem.net/docs/game-references/controls/
    maxAttempts = 2,          -- 最多尝试热启动车辆次数
    difficulties = {
        -- 如果您想要取消某个类别的热启动（如以下自行车），请取消注释
        [0] = { 'easy', 'medium', 'easy' },      -- 轿车
        [1] = { 'easy', 'easy', 'easy' },        -- 轿车
        [2] = { 'easy', 'medium', 'easy' },      -- 轿车
        [3] = { 'medium', 'medium', 'easy' },    -- 双门轿车
        [4] = { 'medium', 'medium', 'easy' },    -- 肌肉车
        [5] = { 'medium', 'medium', 'medium' },  -- 运动经典
        [6] = { 'medium', 'medium', 'medium' },  -- 运动
        [7] = { 'medium', 'hard', 'hard' },      -- 超级
        [8] = { 'easy', 'easy', 'easy' },        -- 摩托车
        [9] = { 'easy', 'medium', 'easy' },      -- 越野车
        [10] = { 'easy', 'easy', 'easy' },       -- 工业车
        [11] = { 'easy', 'easy', 'easy' },       -- 公用事业车
        [12] = { 'easy', 'easy', 'easy' },       -- 客车
        --       [13] = { 'easy', 'easy', 'easy' }, -- 自行车
        [14] = { 'easy', 'easy', 'easy' },       -- 船
        [15] = { 'medium', 'hard', 'easy' },     -- 直升机
        [16] = { 'medium', 'hard', 'easy' },     -- 飞机
        [17] = { 'medium', 'medium', 'medium' }, -- 服务
        [18] = { 'hard', 'medium', 'hard' },     -- 紧急情况
        [19] = { 'hard', 'hard', 'hard' },       -- 军事
        [20] = { 'easy', 'medium', 'easy' },     -- 商业
        --        [21] = { 'easy', 'easy', 'easy' }, -- 火车
        --        [22] = { 'easy', 'easy', 'easy' }, -- 开放式轮子？
    }
}

Config.lockpick          = {
    enabled = true,    -- 启用撬锁功能？
    item = 'lockpick',
    chanceOfLoss = 50, -- 撬锁弯曲并丢失物品的概率
    difficulties = {
        -- 如果您想要取消某个类别的撬锁（如以下自行车），请取消注释
        [0] = { 'easy', 'medium', 'easy' },      -- 轿车
        [1] = { 'easy', 'easy', 'easy' },        -- 轿车
        [2] = { 'easy', 'medium', 'easy' },      -- 轿车
        [3] = { 'medium', 'medium', 'easy' },    -- 双门轿车
        [4] = { 'medium', 'medium', 'easy' },    -- 肌肉车
        [5] = { 'medium', 'medium', 'medium' },  -- 运动经典
        [6] = { 'medium', 'medium', 'medium' },  -- 运动
        [7] = { 'medium', 'hard', 'hard' },      -- 超级
        [8] = { 'easy', 'easy', 'easy' },        -- 摩托车
        [9] = { 'easy', 'medium', 'easy' },      -- 越野车
        [10] = { 'easy', 'easy', 'easy' },       -- 工业车
        [11] = { 'easy', 'easy', 'easy' },       -- 公用事业车
        [12] = { 'easy', 'easy', 'easy' },       -- 客车
        --       [13] = { 'easy', 'easy', 'easy' }, -- 自行车
        [14] = { 'easy', 'easy', 'easy' }, -- 船
        [15] = { 'medium', 'hard', 'easy' },     -- 直升机
        [16] = { 'medium', 'hard', 'easy' },     -- 飞机
        [17] = { 'medium', 'medium', 'medium' }, -- 服务
        [18] = { 'hard', 'medium', 'hard' },     -- 紧急情况
        [19] = { 'hard', 'hard', 'hard' },       -- 军事
        [20] = { 'easy', 'medium', 'easy' },     -- 商业
        --        [21] = { 'easy', 'easy', 'easy' }, -- 火车
        --        [22] = { 'easy', 'easy', 'easy' }, -- 开放式轮子？
    }
}

Config.searchingVehicle  = {                                                                     -- 车辆搜索选项
    enabled = true,                                                                              -- 启用搜索车辆以寻找钥匙/战利品
    string = '[L] - 搜索',                                                                     -- 文本用户界面中希望显示的字符串
    hotkey = 7,                                                                                  -- 默认 'S' (78) / https://docs.fivem.net/docs/game-references/controls/
    progressLabel = '正在搜索车辆...',
    timeToSearch = 8,                                                                            -- 搜索车辆所需时间（以秒为单位）
    rewards = {                                                                                  -- 可获得的随机物品
        [1] = { chance = 50, type = 'account', name = 'cash', quantity = math.random(100, 500) }, -- 以金钱作为奖励的示例
        [2] = { chance = 50, type = 'key' },                                                     -- 如果想要将钥匙作为奖励，请取消注释
        --[3] = { chance = 50, type = 'item', name = 'water', label = '水', quantity = 1 },         -- 以物品作为奖励的示例
    }
}